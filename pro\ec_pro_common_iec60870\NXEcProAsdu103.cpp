/**********************************************************************
* NXEcProAsdu103.cpp         author:jjl      date:23/10/2013            
*---------------------------------------------------------------------
*  note: ASDU103报文转换处理实现文件     --召唤通用文件.                                                        
*  
**********************************************************************/

#include "NXEcProAsdu103.h"


/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu103::~TNXEcProAsdu103()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu103::TNXEcProAsdu103(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu(pSeekIns,pLogRecord)
{
	m_pCommonMsg = NULL;
    m_pBody = NULL;
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu103");
}


/**
* @brief         直接从本地生成结果回应，如初始化配置;
* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu103::DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[255] = "";
	if (pBody->vVarData.size() < 105)
	{
		RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令报文长度不足,不进行处理.","TNXEcProAsdu103");
		return EC_PRO_CVT_FAIL;
	}
	//获取返回信息标示符
	m_nRii = (u_int8)(pBody->nRii);
	m_nFun = (u_int8)(pBody->nFun);
	m_nInf = (u_int8)(pBody->nInf);

	//获取召唤的文件名
	char cFileName[255]="";
	char cFilePath[255]="";
	char _cFileName[255]="";
	char _cFileExt[255]="";
	memcpy(cFileName,&(pBody->vVarData[1]),100);
	if (sy_get_file_name(cFileName,cFilePath,_cFileName,_cFileExt) != 0) //去掉命令中可能的路径信息
	{
		RcdErrLogWithParentClass("DirectResFromLocal:召唤通用文件命令报文中文件名格式有误或者为空.","TNXEcProAsdu103");
		return EC_PRO_CVT_FAIL;
	}  
	memset(cFileName,0,255);
	sprintf(cFileName,"%s.%s",_cFileName,_cFileExt);
	
	//获取指定的起始传输位置
	int nBeginSendPos;
	memcpy(&nBeginSendPos,&(pBody->vVarData[101]),4);
	_REVERSE_BYTE_ORDER_32(nBeginSendPos);

	sprintf(cError,"收到召唤通用文件命令,召唤的文件名为:%s,指定的起始位置:%d",cFileName,nBeginSendPos);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");

    m_pBody = pBody;
	//上送文件内容
	if ( _GeneralFileHandle(cFileName,nBeginSendPos,lResult)<0)
	{
		return EC_PRO_CVT_FAIL;
	}
    m_pBody = NULL;
	RcdTrcLogWithParentClass("上送通用文件数据结束.","TNXEcProAsdu103");

	return EC_PRO_CVT_SUCCESS;
}

/**
* @brief         转换规约信息到NX通用消息结构
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lBody：保存生成的规约失败回应(服务端规约有效）
* @return        >=0:成功 <0:失败
*/
int TNXEcProAsdu103::ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lBody)
{
	char cError[255] = "";
	const IED_TB* pIedTb = NULL;
	PRO_FRAME_BODY_LIST::iterator iteBody = pBodyList->begin();
	char cFileName[128] = ""; 

	while(iteBody != pBodyList->end())
	{
        //获取召唤的文件名
        char cFileName[255]="";
        char cFilePath[255]="";
        char _cFileName[255]="";
        char _cFileExt[255]="";
        memcpy(cFileName,&(iteBody->vVarData[1]),100);
        if (sy_get_file_name(cFileName,cFilePath,_cFileName,_cFileExt) != 0) //去掉命令中可能的路径信息
        {
            RcdErrLogWithParentClass("ConvertProToCommonMsg:召唤CSV文件命令报文中文件名格式有误或者为空.","TNXEcProAsdu103");
            iteBody++;
            continue;
        }  
        memset(cFileName,0,255);
        sprintf(cFileName,"%s.%s",_cFileName,_cFileExt);

		//转成NX_MSG:NX_IED_CALL_OSCLIST_ASK
		NX_COMMON_MESSAGE  NxMsg;
		NxMsg.n_msg_topic = NX_TOPIC_COMMAND;
		NxMsg.n_msg_type  = NX_IED_CALL_IEDFILE_ASK;
		pIedTb = m_pModelSeek->GetIedBasicCfgByAddr103(iteBody->nAddr);
		if (pIedTb == NULL)
		{
			sprintf(cError,"ConvertProToCommonMsg:获取设备(IED103ADDR=%d,CPU=%d)的基本信息是异常,请检查该设备配置.",iteBody->nAddr,iteBody->nCpu);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
			iteBody++;
			continue;
		}
		NxMsg.n_obj_id    = pIedTb->n_obj_id;
		NxMsg.n_obj_type  = NX_OBJ_TYPE_NX_IED;
		NxMsg.n_sub_obj_id= 0;
		_ZERO_MEM(NxMsg.c_suffix,255);
		memcpy(NxMsg.c_suffix,cFileName,strlen(cFileName));
        NxMsg.n_backup = NX_IED_IEDFILETYPE_CSV_HMON;
		_ZERO_MEM(cError,255);
		sprintf(cError,"ConvertProToCommonMsg():生成z2000的召唤CSV文件命令（ied=%d,filename=%s,filetype=%d）",NxMsg.n_obj_id,cFileName,NxMsg.n_backup);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");

		lMsg.push_back(NxMsg);
		iteBody++;
	}
	return 0;
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu103::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	//RcdErrLogWithParentClass("ConvertCommonMsgToPro()方法在规约客户端有效,服务端暂不支持","TNXEcProAsdu103");

	//return EC_PRO_CVT_NOSUPPORT;

    m_pCommonMsg = pMsg;
    char cError[255] = "";
    int nRii = -1;

    //1-根据返回的结果NX消息，找到与之匹配的命令。
    int nIed = m_pCommonMsg->n_obj_id;
    const IED_TB * pIedTb = m_pModelSeek->GetIedBasicCfgByID(nIed);

    if ( pIedTb == NULL )
    {
        sprintf(cError,"ConvertCommonMsgToPro(): 收到的召唤CSV文件的结果NX消息中，获取设备（IED=%d）的信息失败。",nIed);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
        m_pCommonMsg = NULL;
        return EC_PRO_CVT_FAIL;
    }

    //获取召唤的文件名
    //获取召唤的文件名
    char cFileName[255]="";
    char cFilePath[255]="";
    char _cFileName[255]="";
    char _cFileExt[255]="";

    PRO_FRAME_BODY_LIST::iterator iteCmd = lCmd.begin();
    PRO_FRAME_BODY ProCmd;
    ASDU_TIME  AsduTime;
    while(iteCmd != lCmd.end())
    {
        if ( (iteCmd->nType == 0x67) && (iteCmd->nAddr == pIedTb->n_outaddr103) ) //命令报文必须召唤录波文件，设备103地址必须匹配.
        { 
            memcpy(cFileName,&(iteCmd->vVarData[1]),100);
            if (sy_get_file_name(cFileName,cFilePath,_cFileName,_cFileExt) != 0) //去掉命令中可能的路径信息
            {
                RcdErrLogWithParentClass("ConvertCommonMsgToPro:召唤CSV文件命令报文中文件名格式有误或者为空.","TNXEcProAsdu103");
                iteCmd++;
                continue;
            }

            sprintf(cError,"ConvertCommonMsgToPro(): fileName:%s,filePath:%s,_fileName:%s,fileExt:%s,commonMsg.c_suffix:%s。",cFileName,cFilePath,_cFileName,_cFileExt,m_pCommonMsg->c_suffix);
            RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");

            memset(cFileName,0,255);
            sprintf(cFileName,"%s.%s",_cFileName,_cFileExt);
            string str(m_pCommonMsg->c_suffix);
            if ( str.find(cFileName) >= 0)
            {
                ProCmd = *iteCmd;
                m_pBody = &ProCmd;
                nRii = iteCmd->nRii;
                break;
            }
        }
        iteCmd++;
    }

    //获取返回信息标示符
    m_nRii = (u_int8)(ProCmd.nRii);
    m_nFun = (u_int8)(ProCmd.nFun);
    m_nInf = (u_int8)(ProCmd.nInf);	//获取返回信息标示符

    if ( nRii == -1 )
    {
        _ZERO_MEM(cError,255);
        sprintf(cError,"ConvertCommonMsgToPro():收到的NX CSV文件召唤结果（IED=%d，FILENAME=%s）没有匹配到命令报文，不做处理。",nIed,cFileName);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
        m_pCommonMsg = NULL;
        return EC_PRO_CVT_FAIL;
    }

    //获取指定的起始传输位置
    int nBeginSendPos;
    memcpy(&nBeginSendPos,&(ProCmd.vVarData[101]),4);
    _REVERSE_BYTE_ORDER_32(nBeginSendPos);

    if ( pMsg->n_result != 0 ) //召唤不成功.
    {
        sprintf(cError,"ConvertCommonMsgToPro(): 收到的召唤CSV文件%s的结果NX消息中，召唤结果为失败.",cFileName);
        RcdErrLogWithParentClass(cError,"ConvertCommonMsgToPro");
        _CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); 

        return EC_PRO_CVT_FAIL;
    }
    
    string str(m_pCommonMsg->c_suffix);
    if ( str.find("CSV_HMON") < 0)
    {
        //上送文件内容
        if ( _GeneralFileHandle(cFileName,nBeginSendPos,lResult)<0)
        {
            return EC_PRO_CVT_FAIL;
        }
    }
    else
    {
        if ( _CSVFileHandle(cFileName,nBeginSendPos,ProCmd.nAddr,lResult)<0)
        {
            return EC_PRO_CVT_FAIL;
        }
    }
    m_pBody = NULL;

    RcdTrcLogWithParentClass("上送CSV文件数据结束.","TNXEcProAsdu103");

    return EC_PRO_CVT_SUCCESS;
}

/**
* @brief		通用文件上送处理函数			
* @param[in]     const char * cFileName:指定的文件名称
* @param[in]    int nBeginSendPos:起始传输位置
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103::_GeneralFileHandle(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cError[500]="";

	//获取文件存放的路径-通用文件路径;
	char cTemp[255]="";
	char cGeneralFilePathName[255]="";  //包含路径的文件名
	BASIC_CFG_TB CfgTb;

	if (!m_pModelSeek->GetBasicCfg(CfgTb))
	{
		RcdErrLogWithParentClass("_GeneralFileHandle():获取基本配置数据失败","TNXEcProAsdu103");
		return -1;
	}

	if (sy_format_file_path(CfgTb.str_file_path.c_str(),cTemp) < 0) 
	{
		sprintf(cError,"_GeneralFileHandle():格式化通用文件路径[%s]出错.",CfgTb.str_file_path.c_str());
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}

    sprintf(cGeneralFilePathName,"%s%s%s%s",cTemp,"通用文件",FILE_PATH_OPT_STR,cFileName);

	//判断文件是否存在
	FILE_PROPERTY_INF FileInfo;
	if (sy_get_file_property(cGeneralFilePathName,&FileInfo) != 0)
	{
		sprintf(cError,"通用文件[%s]不存在.",cGeneralFilePathName);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");
		_CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); //文件不存在,转成空文件报文.
		return 0;
	}
	
	//存在则处理
	if ( _CvtFileToFrameBody(cGeneralFilePathName,nBeginSendPos,&FileInfo,lResult) < 0)
	{
		sprintf(cError,"转换通用文件[%s]到规约信息体时出错.",cGeneralFilePathName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}
	return 0;
}

/**
* @brief		CSV文件上送处理函数			
* @param[in]     const char * cFileName:指定的文件名称
* @param[in]    int nBeginSendPos:起始传输位置
* @param[in]    int nAddr:设备地址
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103::_CSVFileHandle(IN const char * cFileName,IN int nBeginSendPos,IN int nAddr,OUT PRO_FRAME_BODY_LIST & lResult)
{
    char cError[500]="";

    //获取文件存放的路径-CSV文件路径;
    char cTemp[255]="";
    char cGeneralFilePathName[255]="";  //包含路径的文件名
    BASIC_CFG_TB CfgTb;

    if (!m_pModelSeek->GetBasicCfg(CfgTb))
    {
        RcdErrLogWithParentClass("_CSVFileHandle():获取基本配置数据失败","TNXEcProAsdu103");
        return -1;
    }

    if (sy_format_file_path(CfgTb.str_file_path.c_str(),cTemp) < 0) 
    {
        sprintf(cError,"_GeneralFileHandle():格式化CSV文件路径[%s]出错.",CfgTb.str_file_path.c_str());
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
        return -1;
    }

    const SUBSTATION_TB * pStation =  m_pModelSeek->GetSubStationBasicCfg();
    if( pStation == NULL )
    {
        sprintf(cError,"_CSVFileHandle():获得场站信息失败,无法生成路径");
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu13");
        return -1;
    }
    // 根据103地址获得设备名称、设备id及设备所属的变电站ID
    const IED_TB * pIed = m_pModelSeek->GetIedBasicCfgByAddr103(nAddr);
    if( pIed == NULL )
    {
        sprintf(cError,"_CSVFileHandle():获得103Addr=%d的设备配置失败,无法生成路径",nAddr);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu13");
        return -1;
    }
    string sSubName = pStation->str_aliasname;
    int nIedId = pIed->n_obj_id;
    string sIedName = pIed->str_aliasname;
    sprintf(cGeneralFilePathName,"%s%s%s%sIED_%d_%s%s%s%s%s",cTemp,FILE_PATH_OPT_STR,sSubName.c_str(),FILE_PATH_OPT_STR,nIedId,sIedName.c_str(),FILE_PATH_OPT_STR,"CSV_HMON",FILE_PATH_OPT_STR,cFileName);

    //判断文件是否存在
    FILE_PROPERTY_INF FileInfo;
    if (sy_get_file_property(cGeneralFilePathName,&FileInfo) != 0)
    {
        sprintf(cError,"CSV文件[%s]不存在.",cGeneralFilePathName);
        RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");
        _CvtEmptyFileToFrameBody(nBeginSendPos,cFileName,lResult); //文件不存在,转成空文件报文.
        return 0;
    }

    //存在则处理
    if ( _CvtFileToFrameBody(cGeneralFilePathName,nBeginSendPos,&FileInfo,lResult) < 0)
    {
        sprintf(cError,"转换CSV文件[%s]到规约信息体时出错.",cGeneralFilePathName);
        RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
        return -1;
    }
    return 0;
}

/**
* @brief		做成空文件报文,来响应文件不存在的情况.			
* @param[in]    int nBeginSendPos:命令中的起始传输位置.
* @param[in]    const char * cFileName:文件名称(不包含路径).
* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103::_CvtEmptyFileToFrameBody(IN int nBeginSendPos,IN const char * cFileName,OUT PRO_FRAME_BODY_LIST & lResult)
{
	char cReadData[200]="";
	CTimeConvert CCTime;
	string sFileTime;
	time_t tNow = time(NULL);
	CHAR_HANDLE hChar;

	CCTime.SetTime(tNow,0);
	CCTime.GetCP56TIMe(sFileTime);

	memcpy(cReadData,cFileName,strlen(cFileName)>100?100:strlen(cFileName));
	memcpy(&cReadData[100],sFileTime.c_str(),7);

	hChar.mypchar=cReadData;
	hChar.charlenght=111; //通用文件数据块中，除文件内容外，前面的111个字节（文件名100，文件时间7，文件长度4）
	
	int nLast=0;   //后续标志位.

	_FormatGeneralToFrameBody(nBeginSendPos,&hChar,nLast,lResult);

	return 0;
	
}

/**
* @brief		将文件处理成规约消息体可变部分(不包括 返回信息标示符和后续标志位).			
* @param[in]    const char * cGeneralFilePathName:文件名(包含路径)
* @param[in]    int nBeginSendPos:起始传输位置.
* @param[in]    _FILE_PROPERTY_INF _f_FileInfo:文件信息.
* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103::_CvtFileToFrameBody(IN const char * cGeneralFilePathName,IN int nBeginSendPos,IN FILE_PROPERTY_INF * pFileInfo,OUT PRO_FRAME_BODY_LIST & lResult)
{
	CTimeConvert CCTime;
	string sFile7Tm;
	int  nTotalFrameNumber;  //总帧数.
	char cReadData[5000]="";  
	int  nReadLenght=0;		   //读取到的长度
	int nLast=1;               //后续帧标志
	CHAR_HANDLE hChar;         //字符串处理体

	//打开通用文件
	CFileOperate CGeneralFile;
	char cError[500]="";

	if ( !(CGeneralFile.OpenFile(cGeneralFilePathName,CFileOperate::modeRead)))
	{
		sprintf(cError,"读取通用文件%s失败.",cGeneralFilePathName);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
		return -1;
	}
	//设置起始传输位置:起始传输位置指文件被组成传输数据块以后的相对于文件头的位置,因此,对应于实际文件应该要减去数据块头的字节数.
	CGeneralFile.SeekPosition(nBeginSendPos,CFileOperate::begin);

	//获取帧传输可变部分的最大字节数
	int nMaxVarDataLenght=GetAsduVarDataMaxLen()-2;//除去返回信息标识符和后续标志位.

	char cFileDataHead[112]=""; //传输文件数据的头部分(文件名100+时间7+文件长度4)
	//
	memcpy(cFileDataHead,pFileInfo->chName,strlen(pFileInfo->chName)>100?100:strlen(pFileInfo->chName));  //文件名
	CCTime.SetTime(pFileInfo->nLastTime,0);
	CCTime.GetCP56TIMe(sFile7Tm);
	memcpy(&cFileDataHead[100],sFile7Tm.c_str(),7);
	_REVERSE_BYTE_ORDER_32(pFileInfo->nSize);
	memcpy(&cFileDataHead[107],&(pFileInfo->nSize),4);

	//计算出总帧数
	nTotalFrameNumber=(CGeneralFile.GetFileLength()+111-nBeginSendPos) / nMaxVarDataLenght;
	if ((CGeneralFile.GetFileLength()+111-nBeginSendPos) % nMaxVarDataLenght > 0 )  nTotalFrameNumber++;
	nLast=1;
	sprintf(cError,"文件需要分为%d帧传输.",nTotalFrameNumber);
	RcdTrcLogWithParentClass(cError,"TNXEcProAsdu103");

	for (int i=0; i<nTotalFrameNumber;i++)
	{
		if( i == 0 ) 
		{   //第一帧:将111字节的文件信息加上读出的文件内容组成一帧报文.
			nReadLenght = CGeneralFile.ReadData(&cReadData[111],nMaxVarDataLenght-111);
			if (nReadLenght == 0)
			{
				CGeneralFile.CloseFile();
				_ZERO_MEM(cError,500);
				sprintf(cError,"_CvtFileToFrameBody:读取文件%s内容为空，终止处理。",cGeneralFilePathName);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
				//return -1;
			}
			memcpy(cReadData,cFileDataHead,111);
			nReadLenght+=111;
		}
		else
		{
			nReadLenght = CGeneralFile.ReadData(cReadData,nMaxVarDataLenght);
			if (nReadLenght == 0)
			{
				CGeneralFile.CloseFile();
				_ZERO_MEM(cError,500);
				sprintf(cError,"_CvtFileToFrameBody:读取文件%s内容时出错，终止处理。",cGeneralFilePathName);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu103");
				return -1;
			}
		}
		hChar.mypchar=cReadData;
		hChar.charlenght = nReadLenght;

		if(i == nTotalFrameNumber-1) nLast=0;  //最后一帧置0.

		_FormatGeneralToFrameBody(nBeginSendPos,&hChar,nLast,lResult);

		memset(cReadData,0,nMaxVarDataLenght);
		memset(&hChar,0,sizeof(hChar));
		nBeginSendPos+=nMaxVarDataLenght;
	}
	CGeneralFile.CloseFile();
	return 0;
	
}

/**
* @brief		根据传入的规约信息体的可变部分,做成规约信息体.并压入列表.
* @param[in]    int nBeginSendPos:起始传输位置.
* @param[in]    char * cReadData:单帧数据内容
* @param[in]     in nLast: 后续帧标志位
* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
* @return		0-执行成功；其他-执行失败
**/
int TNXEcProAsdu103::_FormatGeneralToFrameBody(IN int nBeginSendPos,IN CHAR_HANDLE * phChar,IN int nLast,OUT PRO_FRAME_BODY_LIST & lResult)
{
	PRO_FRAME_BODY FrameBody;

	FrameBody.nType			= 0x68;
	FrameBody.nVsq			= 0x81;
	FrameBody.nCot			= 0x00;

	FrameBody.nSubstationAdd= m_pModelSeek->GetSubStationBasicCfg()->n_outaddr103;
	FrameBody.nAddr			= (NULL==m_pBody)?0x00:m_pBody->nAddr;
	FrameBody.nCpu			= (NULL==m_pBody)?0x00:m_pBody->nCpu;
	FrameBody.nZone			= 0;

	FrameBody.nFun			= m_nFun;
	FrameBody.nInf			= m_nInf;

	//可变部分
	FrameBody.vVarData.push_back(m_nRii);
	FrameBody.vVarData.push_back(nLast);
	FrameBody.vVarData.resize(2 + 4);
	memcpy(&(FrameBody.vVarData[2]),&nBeginSendPos,4);
	FrameBody.vVarData.resize(2 + 4 + phChar->charlenght);
	memcpy(&(FrameBody.vVarData[2 + 4]),phChar->mypchar,phChar->charlenght);
	lResult.push_back(FrameBody);
	FrameBody.vVarData.clear();

	return 0;
}

