/**********************************************************************
* NXEcProAsdu1_GWS.cpp         author:sl     date:04/09/2014            
*---------------------------------------------------------------------
*  note: 国网104 Asdu1报文转换处理实现文件:相比较南网103,当对全站进行总招时,子站上送所有设备的通信状态和运行状态.南网只上送通信状态.                                                             
*  
**********************************************************************/

#include "NXEcProAsdu1_GWS.h"


/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu1GWS::~TNXEcProAsdu1GWS()
{

}

/***********************************************************************************
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
**********************************************************************************/
TNXEcProAsdu1GWS::TNXEcProAsdu1GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu1(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu1GWS");
}
/**
* @brief         转换NX事件子集列表格式为asdu1信息结构列表
* @param[in]     int nEventType:事件类型
* @param[in]     u_int8 nSin:附加信息
* @param[out]    ASDU1_INFO_LIST & lAsdu1 :ASDU1信息列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu1GWS::_CvtNxEventSubFiledToAsdu1Info(IN int nEventType,IN u_int8 nSin,OUT ASDU1_INFO_LIST & lAsdu1)
{
	ASDU_INFO_OBJ InfoObj;
	ASDU1_INFO Asdu1;
	bool bGetInfoObj = false;
	char cError[255]="";

	sprintf(cError,"[debug]---------------->进入ASDU1处理[_CvtNxEventSubFiledToAsdu1Info]");
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");

	// 获得变电站地址
	const SUBSTATION_TB * pStation = m_pModelSeek->GetSubStationBasicCfg();
	if( pStation == NULL )
		return EC_PRO_CVT_FAIL;
	int16 nSubstationAdd = pStation->n_outaddr103;

	sprintf(cError,"[debug]---------------->进入ASDU1处理[获取变电站地址完成]");
	RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");

	// 获得信息接收时间
	time_t nRevTime = m_pEventMsg->n_send_utctm;

	const IED_TB * pIed = NULL;
	// 转换NX事件子集结构为ASDU1信息结构
	NX_EVENT_MSG_SUBFILED_LIST::iterator ite = m_pEventMsg->list_subfields.begin();
	while ( ite != m_pEventMsg->list_subfields.end() )
	{
		// 地址信息
		Asdu1.Addr.nSubstationAdd = nSubstationAdd;

		sprintf(cError,"[debug]---------------->进入ASDU1处理[转换NX时间子集结构体循环]");
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
		pIed =  m_pModelSeek->GetIedBasicCfgByID(ite->n_obj_id);
		if( pIed == NULL )
		{
			++ite;
			continue;
		}
		sprintf(cError,"[debug]---------------->进入ASDU1处理[获取到PIED成功，且不为空]");
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
		if ((pIed->e_psrtype == 23)&&( nEventType == NX_IED_EVENT_HARDTRAP_REPORT ))
		{
			sprintf(cError,"[debug]---------------->进入ASDU1处理[判断为硬压板事件，IED:%d,cpu:%d,硬压板ID为：%d]",ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
			int nReal;
			if (0 !=__DBQueryRealIedByFid(ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,nReal))
			{
				sprintf(cError,"_DirectResEventCfg():获取设备压板编号[%d]的真实IED失败.",ite->n_sub_sub_obj_id);
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
				return EC_PRO_CVT_FAIL;
			}
			int nRealIed = nReal/1000;
			int nRealCpu = nReal%1000;

			pIed =  m_pModelSeek->GetIedBasicCfgByID(nRealIed);

			if (pIed == NULL)
			{
				sprintf(cError,"_DirectResEventCfg():pIed指针为空,返回失败");
				RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
				return EC_PRO_CVT_FAIL;
			}

			Asdu1.Addr.nAddr = pIed->n_outaddr103;
			Asdu1.Addr.nCpu  = nRealCpu;

			/*bGetInfoObj = __DoGetHardInfoObj(&(*ite),nRealIed,nRealCpu,InfoObj);*/
			bGetInfoObj = __DoGetDiInfoObj(&(*ite),InfoObj);
		}
		else
		{
			Asdu1.Addr.nAddr = pIed->n_outaddr103;
			Asdu1.Addr.nCpu  = ite->n_sub_obj_id;

			if( nEventType == NX_IED_EVENT_ALARM_REPORT )    // 告警
			{
				bGetInfoObj = __DoGetAlarmInfoObj(&(*ite),InfoObj);
			}
			else if( nEventType == NX_IED_EVENT_HARDTRAP_REPORT ) // 开关量
			{
				bGetInfoObj = __DoGetDiInfoObj(&(*ite),InfoObj);
			}
			else if( nEventType == NX_IED_EVENT_COMMU_REPORT )  // 通信状态
			{
				bGetInfoObj = __DoGetCommuStatusInfoObj(Asdu1.Addr,&(*ite),InfoObj);
			}
		}
		

		if( !bGetInfoObj )
		{
			++ite;
			continue;
		}

		_ZERO_MEM(cError,255);
		sprintf(cError,"_CvtNxEventSubFiledToAsdu1Info():msg_type=%d,IedId=%d,LD=%d,ID=%d nValue=%d 转换为ASDU1:addr=%d FUN=%d INF=%d DPI=%d",
			m_pEventMsg->n_msg_type,ite->n_obj_id,ite->n_sub_obj_id,ite->n_sub_sub_obj_id,ite->n_value,
			Asdu1.Addr.nAddr,InfoObj.nFun,InfoObj.nInf,InfoObj.nDpi);
		RcdTrcLogWithParentClass(cError,"TNXEcProAsdu1");

		m_CsgLogDataUnit.str_dataobject = "保护设备主动上送.";
		m_CsgLogDataUnit.str_dataauxiliary = cError;
		CsgLogOutRun(m_CsgLogDataUnit);

		// 信息标识符
		Asdu1.InfoObj.nFun = InfoObj.nFun;
		Asdu1.InfoObj.nInf = InfoObj.nInf;
		Asdu1.InfoObj.nDpi = InfoObj.nDpi;

		// 时间
		Asdu1.InfoTime.nInfoHappenUtc = ite->n_curvalueutctm ;
		Asdu1.InfoTime.nInfoHappenMs  = ite->n_curms;
		Asdu1.InfoTime.nInfoRcvUtc    = nRevTime;
		Asdu1.InfoTime.nInfoRcvMs     = 0;

		// 品质
		Asdu1.nQuality = ite->n_quality;

		// 附加信息
		Asdu1.nSin = nSin;

		lAsdu1.push_back(Asdu1);
		++ite;
		bGetInfoObj = false;
		pIed = NULL;
	}

	if( lAsdu1.size() <= 0 )
		return EC_PRO_CVT_FAIL;

	return 0;
}
/*****************************************************************
* @brief         根据开关量从数据库中查找指定设备ID.
* @param[in]     int nIed_Id:设备ID.
* @param[out]    INT nStation_Id: 厂站ID.
* @return        int 0-成功 其它失败
****************************************************************/
int TNXEcProAsdu1GWS::__DBQueryRealIedByFid(IN int obj_id,IN int n_Cpu,IN int n_field_id,OUT int & nReal)
{
	INXEcModelMgr * pModelMgr;
	DB_OPER_PARAM suParam;				 // 数据库查询参数结构体
	DB_FIELD_DATA suField;               //字段结构体
	DB_CONDITION  suCondition;           //条件结构体
	CPlmRecordSet RcdSet;				 //结果数据集

	char cDBError[255]="";
	char cLogMsg[500]="";

	sprintf(cLogMsg,"[debug]---------------->进入ASDU1处理[__DBQueryRealIedByFid，IED:%d,cpu:%d,硬压板ID为：%d]",obj_id,n_Cpu,n_field_id);
	RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu1GWS");

	//要查询出的连个字段
	suField.str_fd_name="strbackup1";			//真实ID
	suParam.lst_fddata.push_back(suField);


	//查询表名
	suParam.lst_tablename.push_back("nx_t_ied_hardstrap_cfg");

	//查询条件1  设备ID
	suCondition.str_cdt_name="strap_code";
	char cIed_Id[50]="";
	sprintf(cIed_Id,"%d",n_field_id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询条件1  设备ID
	suCondition.str_cdt_name="ied_obj";
	sprintf(cIed_Id,"%d",obj_id);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询条件1  设备ID
	suCondition.str_cdt_name="ld_code";
	sprintf(cIed_Id,"%d",n_Cpu);
	suCondition.str_cdt_value=cIed_Id;
	suCondition.e_cdt_type		=CDT_TYPE_EQUAL;
	suCondition.e_cdt_val_type  =FD_TYPE_NUMERIC;
	suParam.lst_condition.push_back(suCondition);

	//查询nx_t_ied_comtrade_list历史录波列表中,是否存在符合指定条件的记录.
	pModelMgr=m_pModelSeek->GetModelMgrObj();	
	int nRet=pModelMgr->dbm_select_records(&suParam,RcdSet,false,cDBError);
	if(nRet!=0)
	{
		_clear_db_opera_param(suParam); // 清除查询信息列表
		sprintf(cLogMsg,"__DBQueryRealIedByFid():全站查询n_field_id为%d的信息时出错[%s]",n_field_id,cDBError);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu42GWS");
		return EC_PRO_CVT_FAIL;
	}

	_clear_db_opera_param(suParam); // 清除查询信息列表

	//检查结果
	UINT nRecordNum=0;
	RcdSet.get_record_num(nRecordNum);
	if(0==nRecordNum)//没有找到对应文件
	{
		RcdSet.clear_result();
		memset(cLogMsg,0,500);
		sprintf(cLogMsg,"__DBQueryRealIedByFid():全站的ied中,n_field_id为%d的配置不存在.",n_field_id);
		RcdErrLogWithParentClass(cLogMsg,"TNXEcProAsdu42GWS");
		return EC_PRO_CVT_FAIL;
	}
	string strValue;
	RcdSet.get_field_value_row(1,1,strValue);
	nReal = atoi(strValue.c_str());

	RcdSet.clear_result();
	memset(cLogMsg,0,500);
	sprintf(cLogMsg,"__DBQueryRealIedByFid():获取n_field_id：%d,真实ID：%d.",n_field_id,nReal);
	RcdTrcLogWithParentClass(cLogMsg,"TNXEcProAsdu42GWS");
	return EC_PRO_CVT_SUCCESS;
}
/**
* @brief         获得开关量变位事件信息体标识对象信息
* @param[in]     NX_EVENT_FIELD_STRUCT * pFiled：事件信息子集
* @param[out]    ASDU_INFO_OBJ & InfoObj:信息对象
* @return        bool:true-成功 false-失败
*/
bool TNXEcProAsdu1GWS::__DoGetHardInfoObj(IN NX_EVENT_FIELD_STRUCT * pFiled,IN int & nIed,IN int & nCpu,OUT ASDU_INFO_OBJ & InfoObj)
{
	char cError[255] = "";
	const STRAP_TB * pStrapTb = NULL;

	if( pFiled == NULL )
	{
		RcdErrLogWithParentClass("__DoGetHardInfoObj()：输入事件子集参数为NULL,无法获取","TNXEcProAsdu1GWS");
		return false;
	}
	// 信息标识符
	pStrapTb = m_pModelSeek->GetIedDiCfg(nIed,nCpu,pFiled->n_sub_sub_obj_id);
	if( pStrapTb == NULL )
	{
		sprintf(cError,"__DoGetHardInfoObj():获取IedId=%d,LD=%d,DiID=%d的信息标识对象失败",
			nIed,nCpu,pFiled->n_sub_sub_obj_id);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu1GWS");
		return false;
	}
	InfoObj.nFun = pStrapTb->n_outfun103;
	InfoObj.nInf = pStrapTb->n_outinf103;
	InfoObj.nDpi = pFiled->n_value +1;
	return true;
}