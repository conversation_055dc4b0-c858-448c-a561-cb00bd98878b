	// 根据通用分类信息区分命令类型
	if( ( Asdu10Info.nCot == 0x28) && (Asdu10Info.InfoObj.nFun == 0xfe) )  // 控制命令
	{
		if( Asdu10Info.InfoObj.nInf == 0xf9 ) // 预校
		{
			nRet = _CvtAsdu10InfoToRCtlCheck(nGropTitleType,Asdu10Info,lMsg);
		}
		else if ( Asdu10Info.InfoObj.nInf == 0xfa) // 执行
		{
			nRet = _CvtAsdu10InfoToRCtlExc(nGropTitleType,Asdu10Info,lMsg);
		}
		else
		{
			//错误
			sprintf(cError,"ConvertProToCommonMsg()中收到的控制命令nInf=%d,错误不处理",Asdu10Info.InfoObj.nInf);
			RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		}
	}
	else
	{
		//其它命令暂不支持处理
		sprintf(cError,"ConvertProToCommonMsg()中不支持cot=%d、Fun=%d、Inf=%d的消息处理",
			    Asdu10Info.nCot,Asdu10Info.InfoObj.nFun,Asdu10Info.InfoObj.nInf);
		RcdErrLogWithParentClass(cError,"TNXEcProAsdu10");
		nRet = EC_PRO_CVT_NOSUPPORT;
	}