# 总体结构与模块关系
主管理器（Main Controller/Node Manager）
总线接口（BusSwap）
服务中介（Mediator）
对外通信管理（Node/Channel/Protocol）
网络监听（NetListen）
模型管理及服务（Model Manager/Model Access）
规约库（Protocol Library）

# 主管理器（Main Controller/Node Manager）
## 主程序初始化流程 (CNXMainController)

### 动态库加载顺序
```cpp
int CNXMainController::_LoadOperationLib() {
    1. __LoadEcModelAccessLib()  // 加载模型访问库
    2. __LoadSrvMedLib()        // 加载服务中介库
    3. __LoadBusSwapLib()       // 加载总线交换库
    4. __LoadNetListenLib()     // 加载网络监听库
    5. __LoadNodeMgrLib()       // 加载节点管理库
}
```

### 系统启动流程
```cpp
int CNXMainController::_StartOperation() {
    1. _OpenLogRecord()           // 打开日志
    2. _LoadOperationLib()        // 加载动态库
    3. __SetEcRunEnvParam()      // 设置系统运行环境变量
    4. __StartBusSwap()          // 启动总线交换业务
    5. __StartNodeManger()       // 启动通信管理业务
    6. __StartNetListen()        // 启动网络监听服务
    7. _StartCmdHandleThread()   // 启动命令处理线程
    8. __InitSubjectInfo()       // 初始化目标者信息
}
```
10. 规约库动态加载流程
13. 节点管理架构

# 总线接口（BusSwap）
## BusSwap 观察者模式实现
### 角色定义
```cpp
// 请求者（观察者）
class CNXRequester {
    // 接收消息的回调
    virtual void OnReceiveMessage(NX_MESSAGE& msg);
};

// 目标者（被观察者）
class CNXSubject {
    // 接收消息的回调
    virtual void OnReceiveMessage(NX_MESSAGE& msg);
};

// BusSwap同时具有两种角色
class CNXEcBusSwap {
private:
    CNXSubject* m_pSubject;      // 作为目标者
    CNXRequester* m_pObserver;   // 作为观察者
};
```

### 主要接口
```cpp
class CNXEcBusSwap {
public:
    // 注册接口
    bool EnrollRequester(int OId, CNXRequester* pOb);     // 注册请求者
    bool EnrollSubject(int Sid, CNXSubject* pSub);        // 注册目标者
    
    // 注销接口
    bool CancelRequester(int OId);                        // 注销请求者
    bool CancelSubject(int Sid);                         // 注销目标者
    
    // 订阅管理
    bool SubscribeInfo(int OId, vector<int> vInfType, vector<int> vDev);  // 订阅信息
    bool AnnulInfo(int oid, int nInfoType);                               // 取消订阅
    
    // 消息发送
    bool SendMessageToRequester();  // 向请求者发送信息
    bool SendMessageToSubject();    // 向目标者发送信息
    
    // 查找接口
    CNXRequester* SearchRequester(int nOId);  // 搜索请求者
    CNXSubject* SearchSubject(int nSid);      // 搜索目标者
};
```

### 双向通信流程
```
总线数据流向：
1. 上行：外部设备 -> BusSwap(作为目标者) -> 请求者(业务模块)
2. 下行：业务模块(作为目标者) -> BusSwap(作为请求者) -> 外部设备
```

### 具体实现

1. 数据接收处理：
```cpp
// 1. 上行数据（从外部设备到业务模块）
void CNXEcBusSwap::OnReceiveFromBus(BUS_MESSAGE& msg) {
    // BusSwap作为目标者接收总线数据
    // 转发给注册的请求者（业务模块）
    SendMessageToRequester(msg);
}

// 2. 下行数据（从业务模块到外部设备）
void CNXEcBusSwap::OnReceiveFromModule(MODULE_MESSAGE& msg) {
    // BusSwap作为请求者接收业务模块数据
    // 转发到总线
    SendMessageToSubject(msg);
}
```

2. 订阅机制实现：
```cpp
bool CNXEcBusSwap::SubscribeInfo(
    int OId,                    // 请求者ID
    vector<int> vInfType,       // 关心的信息类型
    vector<int> vDev           // 关心的设备
) {
    // 记录订阅关系
    auto requester = SearchRequester(OId);
    if(requester) {
        requester->SetInterestedTypes(vInfType);
        requester->SetInterestedDevices(vDev);
        return true;
    }
    return false;
}
```

3. 消息分发：
```cpp
bool CNXEcBusSwap::SendMessageToRequester() {
    // 将总线数据分发给关注的业务模块
    for(auto requester : m_requesters) {
        if(requester->IsInterestedIn(msgType)) {
            requester->OnReceiveMessage(msg);
        }
    }
    return true;
}

bool CNXEcBusSwap::SendMessageToSubject() {
    // 将业务模块数据发送到对应的目标设备
    for(auto subject : m_subjects) {
        if(subject->OwnsDevice(deviceId)) {
            subject->OnReceiveMessage(msg);
        }
    }
    return true;
}
```

### 设计说明

BusSwap 需要同时向请求者和目标者发送信息的原因：

1. 双向数据流处理：
   - 作为中间层，需要处理上行和下行数据
   - 上行：外部设备 → 业务模块
   - 下行：业务模块 → 外部设备

2. 双重角色：
   - 作为目标者：接收外部设备数据，转发给请求者（业务模块）
   - 作为请求者：接收业务模块数据，转发给目标者（外部设备）

3. 设计优势：
   - 业务模块可以订阅关心的数据类型和设备
   - 外部设备的数据可以精确投递给关心的业务模块
   - 业务模块的控制命令可以准确发送到目标设备
   - 实现了高度解耦的数据交换机制

# 服务中介（Mediator）
## 通信流程

### 组件间通信关系
```
NetListen (网络监听)
    ↓
Protocol (规约解析) 
    ↓
ProOperation (规约操作)
    ↓
Mediator (服务中介)
    ↓
BusSwap (总线交换)
```

### 消息处理流程

#### 网络监听层
```cpp
// CNXEcNetListen 通过三个线程实现不间断监听
1. 业务处理线程 (__OperaHandlerLoop)
   - 处理通信对象的打开
   - 初始化握手
   - 确认数据清理

2. 接收数据线程 (__RcvFrameLoop)
   - 不断接收帧数据
   - 处理接收到的数据

3. 发送数据线程 (__SendFrameLoop)
   - 处理需要发送的数据
   - 发送各类帧（I帧、S帧、控制帧等）
```

#### 数据处理流程
1. 网络数据接收：
```cpp
CNXEcNetListen::OnDataRecv() -> Protocol解析
```

2. 规约解析转换：
```cpp
Protocol -> ProOperation::ProToMsg() -> 转换为通用消息
```

3. 消息路由：
```cpp
ProOperation -> Mediator::SendCommonMsgToSubject() -> 路由到目标组件
```

4. 总线交换：
```cpp
Mediator -> BusSwap::SendToBus() -> 发送到总线
```

#### 线程间数据流转
```
网络数据 -> NetListen (监听端口)
           -> 104ProTransObj (三个线程循环处理)
              1. RcvFrameLoop (接收数据)
              2. OperaHandlerLoop (业务处理)
              3. SendFrameLoop (发送数据)
              -> ProOperation (规约操作)
                 -> Mediator (服务中介)
                    -> BusSwap (总线交换)
```

## Node与ProOperation数据流转

### 整体架构
```cpp
class CNXEcNodeChannel {
private:
    TNXEcProtocol* m_pProtocol;          // 规约实例
    INXEcProOperation* m_pProOperaIns;    // 规约操作实例
    CNXEcLoadProLib* m_pLoadProLib;       // 规约库加载器
    
    // 通信相关
    ICommuTransObj* m_pCommuObj;          // 通信对象
    vector<ConnectionInfo> m_connections;   // 连接信息
};
```

### 数据流转路径

1. 外部数据接收流程：
```
外部设备 
   ↓
Node (通信管理)
   ↓
Protocol (规约解析)
   ↓
ProOperation (数据处理)
   ↓
Mediator (服务中介)
   ↓
BusSwap (总线交换)
```

2. 内部数据发送流程：
```
BusSwap (总线交换)
   ↓
Mediator (服务中介)
   ↓
ProOperation (数据转换)
   ↓
Protocol (规约封装)
   ↓
Node (通信管理)
   ↓
外部设备
```

### Node职责

1. 通信管理：
```cpp
class CNXEcNodeChannel {
    bool HandleExternalComm() {
        // 1. 管理socket连接
        m_pCommuObj->HandleConnection();
        
        // 2. 数据接收
        if(m_pCommuObj->HasData()) {
            // 转发给规约处理
            m_pProtocol->OnDataReceived(data);
        }
        
        // 3. 连接状态监控
        MonitorConnections();
    }
};
```

2. 规约生命周期管理：
```cpp
class CNXEcNodeChannel {
    bool Init() {
        // 1. 加载规约库
        m_pLoadProLib->LoadShareLib(proLibName);
        
        // 2. 创建规约实例
        m_pProtocol = m_pLoadProLib->CreateSrvProIns(&param);
        
        // 3. 创建规约操作实例
        m_pProOperaIns = CreateProOperation();
        
        return true;
    }
};
```

### ProOperation职责

1. 数据处理：
```cpp
class CNXEcProOperation {
    bool HandleData(const PRO_FRAME_BODY& frame) {
        // 1. 规约数据转换为通用消息
        NX_MESSAGE msg;
        if(!ProToMsg(frame, msg)) {
            return false;
        }
        
        // 2. 发送给中介者
        m_pMediator->SendMessage(msg);
        return true;
    }
    
    bool HandleCommand(const NX_MESSAGE& msg) {
        // 1. 通用消息转换为规约数据
        PRO_FRAME_BODY frame;
        if(!MsgToPro(msg, frame)) {
            return false;
        }
        
        // 2. 发送给规约处理
        m_pProtocol->SendFrame(frame);
        return true;
    }
};
```

### 交互流程

1. 数据接收处理：
```cpp
// 1. Node接收外部数据
void CNXEcNodeChannel::OnDataReceived(const char* data) {
    // 转发给规约处理
    m_pProtocol->OnDataReceived(data);
}

// 2. Protocol解析数据
void CNXEcProtocol::OnDataReceived(const char* data) {
    // 解析为规约帧
    PRO_FRAME_BODY frame;
    if(ParseFrame(data, frame)) {
        // 转发给ProOperation处理
        m_pProOperaIns->HandleData(frame);
    }
}

// 3. ProOperation处理数据
void CNXEcProOperation::HandleData(const PRO_FRAME_BODY& frame) {
    // 转换为通用消息
    NX_MESSAGE msg;
    if(ProToMsg(frame, msg)) {
        // 发送给中介者
        m_pMediator->SendMessage(msg);
    }
}
```

2. 数据发送处理：
```cpp
// 1. ProOperation接收命令
void CNXEcProOperation::OnCommand(const NX_MESSAGE& msg) {
    // 转换为规约帧
    PRO_FRAME_BODY frame;
    if(MsgToPro(msg, frame)) {
        // 发送给规约处理
        m_pProtocol->SendFrame(frame);
    }
}

// 2. Protocol封装数据
void CNXEcProtocol::SendFrame(const PRO_FRAME_BODY& frame) {
    // 封装为规约报文
    string data;
    if(EncodeFrame(frame, data)) {
        // 通过Node发送
        m_pNode->SendData(data);
    }
}

// 3. Node发送数据
void CNXEcNodeChannel::SendData(const string& data) {
    // 通过通信对象发送
    m_pCommuObj->Send(data);
}
```

### 关键点说明

1. Node的职责：
- 负责通信层面的管理
- 维护连接状态
- 管理规约实例生命周期
- 提供数据收发接口

2. ProOperation的职责：
- 负责业务层面的处理
- 实现数据转换
- 与中介者交互
- 处理规约相关的业务逻辑

3. 分层设计的优势：
- 职责明确分离
- 便于维护和扩展
- 提高代码复用性
- 降低模块间耦合


# 对外通信管理（Node/Channel/Protocol）
## 规约类继承体系

### 基类与继承关系
```
TNXEcProtocol (抽象基类)
    |-- 纯虚函数: _DoInitProCommuObj()
    |-- 虚函数: Init(), StartProLib()
    |
    ↓
CNXEcSrvProtocol (服务端规约基类)
    |-- 实现: _DoInitProCommuObj()
    |-- 虚函数: StartProLib()
    |
    ↓
CNXEcGW104SrvProtocol (GW104具体实现)
    |-- 重写: StartProLib()
    |-- 构造时设置动态库名: SetRunTimeObjName("libnx_ec_pro_srv_gw104.so")
```

### 规约实例创建流程
1. 节点管理器加载规约库：
```cpp
bool CNXEcNodeChannel::__InitProLib() {
    // 创建规约库加载器
    m_pLoadProLib = new CNXEcLoadProLib();
    
    // 从配置获取规约信息
    PROTOCOL_CFG_TB* pProCfg = m_pClientCfg->p_backup;
    
    // 加载规约动态库
    m_pLoadProLib->LoadShareLib(pProCfg->str_prodllname);
}
```

2. 创建规约实例：
```cpp
// nx_ec_pro_srv_gw104/ec_pro_srv_gw104_export.cpp
INXEcProtocol* CreateSrvProIns(const SRV_PRO_START_PARAM* pParam) {
    // 创建具体规约实例
    CNXEcGW104SrvProtocol* pProtocol = new CNXEcGW104SrvProtocol(pParam);
    
    // 初始化
    if(!pProtocol->Init()) {
        delete pProtocol;
        return NULL;
    }
    
    return pProtocol;  // 返回基类指针
}
```

## 规约通信模式

### 客户端模式与服务端模式区别

1. 服务端模式 (Server Mode)：
```cpp
class CNXEcSrvProtocol : public TNXEcProtocol {
    // 特点：
    // 1. 被动等待连接
    // 2. 监听固定端口
    // 3. 支持多个客户端连接
    
    virtual bool StartProLib() {
        // 1. 创建监听socket
        // 2. 绑定端口
        // 3. 开始监听
        return true;
    }
};
```

2. 客户端模式 (Client Mode)：
```cpp
class CNXEcCliProtocol : public TNXEcProtocol {
    // 特点：
    // 1. 主动发起连接
    // 2. 连接指定IP和端口
    // 3. 支持断线重连
    
    virtual bool StartProLib() {
        // 1. 创建socket
        // 2. 连接服务器
        // 3. 启动重连机制
        return true;
    }
};
```

### 节点配置表区别

1. 服务端节点配置：
```sql
CREATE TABLE SERVER_NODE_CFG (
    n_node_id INT,           -- 节点ID
    n_listen_port INT,       -- 监听端口
    n_max_connections INT,   -- 最大连接数
    str_prodllname TEXT,     -- 规约库名称
    n_protocol_type INT      -- 规约类型
);
```

2. 客户端节点配置：
```sql
CREATE TABLE CLIENT_NODE_CFG (
    n_node_id INT,           -- 节点ID
    str_server_ip TEXT,      -- 服务器IP
    n_server_port INT,       -- 服务器端口
    n_reconnect_interval INT,-- 重连间隔
    str_prodllname TEXT,     -- 规约库名称
    n_protocol_type INT      -- 规约类型
);
```

### 通信流程对比

1. 服务端流程：
```
启动
  ↓
向外注册 (注册为服务提供者)
  ↓
读取节点配置
  ↓
监听通知 (等待客户端连接)
  ↓
收到外部连接?
  ↓
启动规约库
  ↓
设置socket
  ↓
数据收发
```

2. 客户端流程：
```
启动
  ↓
向外注册 (注册为服务使用者)
  ↓
读取节点配置
  ↓
启动各节点规约库
  ↓
建立连接
  ↓
连接成功?
  ↓
数据收发
```

### 实现差异

1. 连接管理：
```cpp
// 服务端连接管理
class ServerConnManager {
private:
    map<string, ConnectionInfo> m_connections;  // IP到连接信息的映射
    
    void OnNewConnection(int fd, const string& ip) {
        // 处理新连接
        m_connections[ip] = ConnectionInfo(fd);
    }
};

// 客户端连接管理
class ClientConnManager {
private:
    ConnectionInfo m_connection;  // 单一连接
    
    void ReconnectLoop() {
        while(!m_bStop) {
            if(!IsConnected()) {
                TryConnect();
            }
            sleep(m_reconnectInterval);
        }
    }
};
```

2. 数据处理：
```cpp
// 服务端数据处理
class ServerDataHandler {
    void OnData(const string& clientIp, const char* data) {
        // 1. 查找对应的客户端连接
        auto conn = m_connections.find(clientIp);
        if(conn != m_connections.end()) {
            // 2. 处理数据
            conn->second.HandleData(data);
        }
    }
};

// 客户端数据处理
class ClientDataHandler {
    void OnData(const char* data) {
        // 直接处理数据
        m_connection.HandleData(data);
    }
};
```

### 使用场景

1. 服务端模式适用于：
- 需要同时服务多个客户端
- 作为数据汇聚点
- 提供统一的服务接口
- 例如：主站系统、数据中心

2. 客户端模式适用于：
- 主动上报数据
- 需要实时获取服务端数据
- 分散的数据采集点
- 例如：RTU、智能终端

### 配置示例

1. 服务端配置：
```ini
[Server_Node]
NodeId=1
ListenPort=2404
MaxConnections=100
ProtocolLib=libnx_ec_pro_srv_gw104.so
ProtocolType=104
```

2. 客户端配置：
```ini
[Client_Node]
NodeId=2
ServerIP=*************
ServerPort=2404
ReconnectInterval=5
ProtocolLib=libnx_ec_pro_cli_gw104.so
ProtocolType=104
```

## 节点管理架构

### 整体架构

```cpp
// 节点管理器
class CNXEcCommuNodeMgr {
    EC_CLIENT_NODE_MAP m_ClientNodeMap;  // 客户端节点映射表
};

// 客户端节点
class CNXEcClientNode {
    EC_NODE_CHANNEL_MAP m_ChannelMap;    // 通道映射表
    EC_CHANNEL_STATUS_MAP m_StatusMap;    // 通道状态映射表
};

// 节点通道
class CNXEcNodeChannel {
    ICommuTransObj* m_pCommuObj;         // 通信对象
    INXEcProtocol* m_pProtocol;          // 规约实例
};
```

### 层次结构

1. 节点管理器 (CNXEcCommuNodeMgr)
   ```
   NodeManager (节点管理器)
        ↓
   ClientNode1 (客户端节点)
        ↓
   Channel1 (通道) --- Channel2 (通道) --- Channel3 (通道)
        ↓
   Protocol (规约) --- Socket (套接字)
   ```

2. 配置结构：
   ```cpp
   struct ECU_CLIENT_TB {
       int nClientID;           // 客户端ID
       string strClientName;    // 客户端名称
       vector<ECU_CHANNEL_TB> channels;  // 通道配置
   };

   struct ECU_CHANNEL_TB {
       int nChannelID;         // 通道ID
       string strIP;           // 通道IP
       int nPort;             // 通道端口
       string strProtocol;    // 使用的规约
   };
   ```

### 主要功能

1. 节点管理器 (CNXEcCommuNodeMgr)：
   ```cpp
   class CNXEcCommuNodeMgr {
       bool StartCommuMgr(COMMU_NODE_RUN_WAY eWay);    // 启动通信
       bool StopCommuMgr(COMMU_NODE_RUN_WAY eWay);     // 停止通信
       bool StartOneNode(int nNodeID);                  // 启动单个节点
       bool StopOneNode(int nNodeID);                   // 停止单个节点
   };
   ```

2. 客户端节点 (CNXEcClientNode)：
   ```cpp
   class CNXEcClientNode {
       bool Init();    // 初始化节点配置
       bool Run();     // 启动所有通道
       bool Stop();    // 停止所有通道
       
       // 网络事件处理
       static void _OnRemoteConnectRecv(ICommuTransObj* pConntObj, 
                                      PUB_NETWORK_ADDR& ConntAddr);
   };
   ```

3. 节点通道 (CNXEcNodeChannel)：
   ```cpp
   class CNXEcNodeChannel {
       bool Run();     // 启动规约通信
       bool Stop();    // 停止规约通信
       bool Init();    // 初始化规约
       
       // 设置通信对象
       int SetNetCommuObj(ICommuTransObj* pCommuObj);
   };
   ```

### 通信流程

1. 启动流程：
   ```
   NodeManager.StartCommuMgr()
        ↓
   ClientNode.Run()
        ↓
   Channel.Run()
        ↓
   Protocol.Init() & Socket.Connect()
   ```

2. 数据接收流程：
   ```
   Socket.OnDataReceived()
        ↓
   Channel.HandleData()
        ↓
   Protocol.ParseData()
        ↓
   ClientNode.ProcessData()
        ↓
   NodeManager.HandleMessage()
   ```

### 关键特性

1. 多级管理：
   - 节点管理器管理多个客户端节点
   - 客户端节点管理多个通道
   - 通道管理具体的通信和规约

2. 状态监控：
   - 每个通道维护自己的状态
   - 节点合成所有通道状态
   - 支持状态变化通知

3. 动态连接：
   - 支持动态建立和断开连接
   - 自动重连机制
   - 连接状态监控

4. 配置管理：
   - 支持多种规约配置
   - 支持多通道配置
   - 支持动态加载规约库

### 使用场景

1. 主站系统：
   ```
   NodeManager
      ↓
   ClientNode (变电站1)
      ↓
   Channel1 (IEC104) --- Channel2 (GW104) --- Channel3 (备用)
   ```

2. 就地系统：
   ```
   NodeManager
      ↓
   ClientNode (主站)
      ↓
   Channel1 (主用) --- Channel2 (备用)
   ```

# 网络监听（NetListen）
## 网络监听和通信流实现机制

### 网络监听架构
```cpp
class CNXEcNetListen {
private:
    // epoll相关
    int m_epollFd;                          // epoll文件描述符
    struct epoll_event* m_events;           // epoll事件数组
    
    // 监听管理
    EC_LISTEN_PORT2OBJ_MAP m_PortListenMap; // 端口到监听对象的映射
    LIST_LISTEN m_PortCfgList;              // 端口配置列表
};
```

### Epoll事件处理流程

1. 初始化epoll：
```cpp
bool CNXEcNetListen::_Init() {
    // 创建epoll实例
    m_epollFd = epoll_create(MAX_EVENTS);
    m_events = new epoll_event[MAX_EVENTS];
    
    // 遍历配置的监听端口
    for(auto& port : m_PortCfgList) {
        // 创建监听socket
        int listenfd = CreateListenSocket(port);
        
        // 添加到epoll
        struct epoll_event ev;
        ev.events = EPOLLIN;
        ev.data.fd = listenfd;
        epoll_ctl(m_epollFd, EPOLL_CTL_ADD, listenfd, &ev);
    }
}
```

2. 事件循环：
```cpp
void CNXEcNetListen::__RcvFrameLoop() {
    while(!m_bStop) {
        // 等待事件
        int nfds = epoll_wait(m_epollFd, m_events, MAX_EVENTS, EPOLL_TIMEOUT);
        
        for(int i = 0; i < nfds; i++) {
            if(m_events[i].data.fd == listenfd) {
                // 新连接到达
                HandleNewConnection(m_events[i].data.fd);
            } else {
                // 数据到达
                HandleData(m_events[i].data.fd);
            }
        }
    }
}
```

### Channel机制

1. Channel定义：
```cpp
class CommunicationChannel {
private:
    int m_fd;                     // socket描述符
    ICommuTransObj* m_pTransObj;  // 通信对象
    CThreadPool* m_pThreadPool;   // 线程池
    
public:
    void OnDataReceived(const char* data, int len) {
        // 将数据包装成任务提交给线程池
        auto task = new DataProcessTask(data, len, m_pTransObj);
        m_pThreadPool->AddTask(task);
    }
};
```

2. 新连接处理：
```cpp
void CNXEcNetListen::HandleNewConnection(int listenfd) {
    // 接受新连接
    struct sockaddr_in addr;
    socklen_t addrlen = sizeof(addr);
    int connfd = accept(listenfd, (struct sockaddr*)&addr, &addrlen);
    
    // 创建Channel
    auto channel = new CommunicationChannel(connfd);
    
    // 添加到epoll监听
    struct epoll_event ev;
    ev.events = EPOLLIN | EPOLLET;  // 边缘触发
    ev.data.ptr = channel;          // 使用ptr存储Channel对象
    epoll_ctl(m_epollFd, EPOLL_CTL_ADD, connfd, &ev);
}
```

3. 数据处理：
```cpp
void CNXEcNetListen::HandleData(epoll_event* ev) {
    auto channel = static_cast<CommunicationChannel*>(ev->data.ptr);
    
    // 读取数据
    char buffer[MAX_BUFFER_SIZE];
    int n = read(channel->GetFd(), buffer, MAX_BUFFER_SIZE);
    
    if(n > 0) {
        // 提交给Channel处理
        channel->OnDataReceived(buffer, n);
    } else if(n == 0) {
        // 连接关闭
        CloseChannel(channel);
    }
}
```

### 线程池处理机制

1. 线程池结构：
```cpp
class CThreadPool {
private:
    vector<thread> m_threads;         // 工作线程
    queue<ITask*> m_taskQueue;        // 任务队列
    mutex m_mutex;                    // 互斥锁
    condition_variable m_condition;    // 条件变量
    
public:
    void AddTask(ITask* task) {
        lock_guard<mutex> lock(m_mutex);
        m_taskQueue.push(task);
        m_condition.notify_one();
    }
};
```

2. 工作线程：
```cpp
void CThreadPool::WorkerThread() {
    while(true) {
        ITask* task = nullptr;
        {
            unique_lock<mutex> lock(m_mutex);
            m_condition.wait(lock, [this]() {
                return !m_taskQueue.empty() || m_bStop;
            });
            
            if(m_bStop && m_taskQueue.empty()) {
                return;
            }
            
            task = m_taskQueue.front();
            m_taskQueue.pop();
        }
        
        // 执行任务
        task->Execute();
        delete task;
    }
}
```

### 数据流转过程

```
网络数据 -> Epoll监听
           |
           v
        Channel (封装socket和通信对象)
           |
           v
        线程池 -> 工作线程
           |
           v
        通信对象 (规约解析和处理)
           |
           v
        规约操作 -> 中介者 -> 总线
```

### 性能优化

1. 边缘触发(EPOLLET)：
```cpp
// 使用非阻塞socket + 边缘触发
int flags = fcntl(sockfd, F_GETFL, 0);
fcntl(sockfd, F_SETFL, flags | O_NONBLOCK);

ev.events = EPOLLIN | EPOLLET;
epoll_ctl(epollfd, EPOLL_CTL_ADD, sockfd, &ev);
```

2. 读取优化：
```cpp
void HandleData(int fd) {
    while(true) {  // 边缘触发模式下需要读完所有数据
        char buffer[MAX_BUFFER_SIZE];
        int n = read(fd, buffer, MAX_BUFFER_SIZE);
        
        if(n < 0) {
            if(errno == EAGAIN || errno == EWOULDBLOCK) {
                // 数据读完了
                break;
            }
            // 错误处理
            break;
        }
        // 处理数据
        ProcessData(buffer, n);
    }
}
```

3. 内存管理：
```cpp
class MemoryPool {
    // 预分配固定大小的内存块
    struct MemBlock {
        char data[BLOCK_SIZE];
        MemBlock* next;
    };
    
    // 使用内存池分配Channel对象
    CommunicationChannel* AllocateChannel() {
        if(m_freeBlocks) {
            auto block = m_freeBlocks;
            m_freeBlocks = block->next;
            return new(block->data) CommunicationChannel();
        }
        return new CommunicationChannel();
    }
};
```
# 模型管理及服务（Model Manager/Model Access）

# 规约库（Protocol Library）

## 规约类继承体系

### 基类与继承关系
```
TNXEcProtocol (抽象基类)
    |-- 纯虚函数: _DoInitProCommuObj()
    |-- 虚函数: Init(), StartProLib()
    |
    ↓
CNXEcSrvProtocol (服务端规约基类)
    |-- 实现: _DoInitProCommuObj()
    |-- 虚函数: StartProLib()
    |
    ↓
CNXEcGW104SrvProtocol (GW104具体实现)
    |-- 重写: StartProLib()
    |-- 构造时设置动态库名: SetRunTimeObjName("libnx_ec_pro_srv_gw104.so")
```

### 规约实例创建流程
1. 节点管理器加载规约库：
```cpp
bool CNXEcNodeChannel::__InitProLib() {
    // 创建规约库加载器
    m_pLoadProLib = new CNXEcLoadProLib();
    
    // 从配置获取规约信息
    PROTOCOL_CFG_TB* pProCfg = m_pClientCfg->p_backup;
    
    // 加载规约动态库
    m_pLoadProLib->LoadShareLib(pProCfg->str_prodllname);
}
```

2. 创建规约实例：
```cpp
// nx_ec_pro_srv_gw104/ec_pro_srv_gw104_export.cpp
INXEcProtocol* CreateSrvProIns(const SRV_PRO_START_PARAM* pParam) {
    // 创建具体规约实例
    CNXEcGW104SrvProtocol* pProtocol = new CNXEcGW104SrvProtocol(pParam);
    
    // 初始化
    if(!pProtocol->Init()) {
        delete pProtocol;
        return NULL;
    }
    
    return pProtocol;  // 返回基类指针
}
```
## 实现自定义 GW104 规约指南

### 必需实现的类

1. 规约主类：
```cpp
// 继承服务端规约基类
class CMyGW104SrvProtocol : public CNXEcSrvProtocol {
protected:
    // 必须实现的虚函数
    virtual bool _DoInitProExplainFactory();  // 初始化报文解析工厂
    virtual bool _DoInitProCvtFactory();      // 初始化规约转换工厂
    virtual bool _DoInitProCommuObj();        // 初始化通信对象
};
```

2. 规约转换工厂：
```cpp
// 继承规约转换工厂基类
class CMyGW104CvtFactory : public CNXEc60870CvtFactory {
public:
    // 必须实现的虚函数
    virtual INXEcProConvertObj* CreateProConvertObj(
        INXEcSSModelSeek* pSeekIns,
        CLogRecord* pLogRecord
    );
};
```

3. 规约转换对象：
```cpp
// 继承规约转换对象基类
class CMyGW104CvtObj : public CNXEc60870CvtObj {
protected:
    // 必须实现的数据转换方法
    virtual bool _DoProToMsg();  // 规约数据转通用消息
    virtual bool _DoMsgToPro();  // 通用消息转规约数据
};
```

4. ASDU处理类：
```cpp
// 需要为每种ASDU类型实现一个处理类
class CMyProAsdu1 : public CNXEcProAsdu1_GWS {
    // 处理遥信变位
    virtual bool _DoYxChange();
};

class CMyProAsdu10 : public CNXEcProAsdu10_GWS {
    // 处理总召唤
    virtual bool _DoGeneralCall();
};
```

### 动态库导出接口

```cpp
// ec_pro_srv_my104_export.cpp
extern "C" {
    // 1. 创建规约实例接口
    INXEcProtocol* CreateSrvProIns(const SRV_PRO_START_PARAM* pParam) {
        CMyGW104SrvProtocol* pProtocol = new CMyGW104SrvProtocol(pParam);
        if(!pProtocol->Init()) {
            delete pProtocol;
            return NULL;
        }
        return pProtocol;
    }

    // 2. 销毁规约实例接口
    bool DestroySrvProIns(INXEcProtocol* pProtocol) {
        if(pProtocol) {
            delete pProtocol;
            pProtocol = NULL;
        }
        return true;
    }
}
```

### 主要功能实现

1. 规约解析：
```cpp
bool CMyGW104SrvProtocol::_DoInitProExplainFactory() {
    // 创建104规约解析工厂
    m_pProExplainFactory = new CNXEcIec104ExplainFactory(m_pLogRecord);
    if(!m_pProExplainFactory) {
        return false;
    }
    return true;
}
```

2. 数据转换：
```cpp
bool CMyGW104CvtObj::_DoProToMsg() {
    // 1. 解析ASDU类型
    int nType = GetAsduType();
    
    // 2. 根据类型创建对应的处理对象
    switch(nType) {
        case 1:  // 遥信
            return (new CMyProAsdu1())->Handle();
        case 10: // 总召唤
            return (new CMyProAsdu10())->Handle();
        // ... 其他ASDU类型
    }
    return false;
}
```

3. 通信对象：
```cpp
bool CMyGW104SrvProtocol::_DoInitProCommuObj() {
    // 创建104通信对象
    PRO_TRANS_OBJ_PARAM param;
    param.pExplainFactory = m_pProExplainFactory;
    param.pLogRecord = m_pLogRecord;
    
    m_pProTransObj = new CNXEc104ProSrvTransObj(&param);
    return (m_pProTransObj != NULL);
}
```

### 配置文件要求

1. 动态库配置：
```ini
[Protocol]
LibName=libnx_ec_pro_srv_my104.so  # 动态库名称
Type=104                            # 规约类型
```

2. 通信参数配置：
```ini
[Communication]
Port=2404       # IEC104标准端口
Timeout=15      # 超时时间(秒)
MaxConnections=5 # 最大连接数
```

### 主要ASDU类型参考

| ASDU类型 | 功能 | 必需实现 |
|---------|------|---------|
| 1 | 单点遥信 | 是 |
| 7 | 遥测 | 是 |
| 10 | 总召唤 | 是 |
| 13 | 参数传输 | 可选 |
| 21 | 遥控 | 是 |
| 42 | 电能量召唤 | 可选 |
| 103 | 时钟同步 | 是 |

### 调试建议

1. 日志记录：
```cpp
// 在关键处理点添加日志
RecordTraceLog("收到ASDU类型: %d", nType);
RecordErrorLog("数据转换失败: %s", error.c_str());
```

2. 报文跟踪：
```cpp
// 记录收发的报文内容
void LogFrame(const unsigned char* pData, int nLen, bool bSend) {
    char szHex[1024] = {0};
    for(int i = 0; i < nLen; i++) {
        sprintf(szHex + i*3, "%02X ", pData[i]);
    }
    RecordTraceLog("%s报文: %s", bSend ? "发送" : "接收", szHex);
}
```

## 动态库加载机制

### 动态库加载流程

1. 加载动态库：
```cpp
// CNXEcLoadProLib 类实现
bool CNXEcLoadProLib::LoadShareLib(const string& strProLibName) {
    // 1. 加载动态库文件
#ifdef __PLATFORM_MS_WIN__
    m_hDllHandle = LoadLibrary(strProLibName.c_str());
#else
    m_hDllHandle = dlopen(strProLibName.c_str(), RTLD_LAZY);
#endif

    // 2. 获取导出函数地址
#ifdef __PLATFORM_MS_WIN__
    m_pCreateSrvProInsFunc = (CREATE_SRV_PRO_INS)GetProcAddress(m_hDllHandle, "CreateSrvProIns");
#else
    m_pCreateSrvProInsFunc = (CREATE_SRV_PRO_INS)dlsym(m_hDllHandle, "CreateSrvProIns");
#endif
}
```

2. 函数指针定义：
```cpp
// 定义函数指针类型
typedef INXEcProtocol* (*CREATE_SRV_PRO_INS)(const SRV_PRO_START_PARAM*);
typedef bool (*DESTROY_SRV_PRO_INS)(INXEcProtocol*);

class CNXEcLoadProLib {
private:
    // 存储获取到的函数指针
    CREATE_SRV_PRO_INS m_pCreateSrvProInsFunc;
    DESTROY_SRV_PRO_INS m_pDestroySrvProInsFunc;
};
```

### 为什么需要 extern "C"

1. C++名称修饰机制：
```cpp
// 不使用 extern "C" 时的名称修饰
class MyProtocol {
    static MyProtocol* Create() { return new MyProtocol(); }
};
// 编译后的符号名可能是: _ZN10MyProtocol6CreateEv

// 使用 extern "C" 后
extern "C" MyProtocol* Create() { return new MyProtocol(); }
// 编译后的符号名就是: Create
```

2. 跨平台兼容性：
```cpp
// Windows 和 Linux 平台通用的导出宏
#ifdef _WIN32
    #define DLL_EXPORT extern "C" __declspec(dllexport)
#else
    #define DLL_EXPORT extern "C" __attribute__((visibility("default")))
#endif

// 使用导出宏
DLL_EXPORT INXEcProtocol* CreateSrvProIns(const SRV_PRO_START_PARAM* pParam);
```

### 动态库符号解析过程

1. dlopen/LoadLibrary:
```cpp
// Linux下加载动态库
void* handle = dlopen("libnx_ec_pro_srv_gw104.so", RTLD_LAZY);
if (!handle) {
    fprintf(stderr, "加载失败: %s\n", dlerror());
    return false;
}

// Windows下加载动态库
HMODULE handle = LoadLibrary("nx_ec_pro_srv_gw104.dll");
if (!handle) {
    fprintf(stderr, "加载失败: %d\n", GetLastError());
    return false;
}
```

2. dlsym/GetProcAddress:
```cpp
// Linux下获取函数地址
CREATE_SRV_PRO_INS createFunc = (CREATE_SRV_PRO_INS)dlsym(handle, "CreateSrvProIns");
if (!createFunc) {
    fprintf(stderr, "获取函数地址失败: %s\n", dlerror());
    return false;
}

// Windows下获取函数地址
CREATE_SRV_PRO_INS createFunc = (CREATE_SRV_PRO_INS)GetProcAddress(handle, "CreateSrvProIns");
if (!createFunc) {
    fprintf(stderr, "获取函数地址失败: %d\n", GetLastError());
    return false;
}
```

### 为什么必须使用 extern "C"

1. C++编译器的名称修饰：
- C++支持函数重载，需要在编译时修饰函数名
- 不同编译器的名称修饰规则不同
- 动态库加载时只能按照原始名称查找符号

2. 跨语言调用：
- C语言没有名称修饰机制
- extern "C" 告诉编译器使用C语言的命名规则
- 确保动态库的符号能被正确找到

3. 示例：
```cpp
// 不使用 extern "C" 的C++函数
void MyFunction(int x) { }
void MyFunction(double x) { }
// 编译后可能的符号名：
// _Z10MyFunctioni
// _Z10MyFunctiond

// 使用 extern "C" 的函数
extern "C" void MyFunction(int x) { }
// 编译后的符号名：
// MyFunction
```

### 动态库加载的完整流程

```cpp
// 1. 定义函数指针类型
typedef INXEcProtocol* (*CREATE_FUNC)(const void*);

// 2. 加载动态库
void* handle = dlopen("libmy104.so", RTLD_LAZY);

// 3. 获取函数地址
CREATE_FUNC createFunc = (CREATE_FUNC)dlsym(handle, "CreateSrvProIns");

// 4. 调用函数创建实例
INXEcProtocol* protocol = createFunc(params);

// 5. 使用实例
protocol->Init();
protocol->StartProLib();

// 6. 清理资源
delete protocol;
dlclose(handle);
```

## 规约转换中的模板方法模式

### 基本结构

1. 基类定义（CNXEc60870CvtObj）：
```cpp
class CNXEc60870CvtObj {
public:
    // 定义算法骨架
    int ConvertProToEventMsg(PRO_FRAME_BODY* pBody, NX_EVENT_MSG_LIST& lMsg);
    int ConvertProToCommonMsg(PRO_FRAME_BODY_LIST* pBodyList, NX_COMMON_MSG_LIST& lMsg);
    
protected:
    // 定义需要子类实现的抽象步骤
    virtual int _CvtNxEventSubFiledToAsdu1Info() = 0;
    virtual bool _DoGetHardInfoObj() = 0;
    virtual bool _DoGetCommuStatusInfoObj() = 0;
};
```

2. 子类实现（CNXEc60870CvtObjGWS）：
```cpp
class CNXEc60870CvtObjGWS : public CNXEc60870CvtObj {
protected:
    // 实现特定步骤
    virtual int _CvtNxEventSubFiledToAsdu1Info() override {
        // GW104特定的转换逻辑
    }
    
    virtual bool _DoGetHardInfoObj() override {
        // GW104特定的硬件信息获取逻辑
    }
};
```

### 具体应用

1. ASDU报文转换：
```cpp
// 基类 TNXEcProAsdu1
class TNXEcProAsdu1 {
protected:
    // 定义通用转换流程
    virtual int _CvtNxEventSubFiledToAsdu1Info() {
        // 1. 获取基本信息
        // 2. 调用子类实现的特定步骤
        // 3. 处理结果
    }
};

// 子类 TNXEcProAsdu1GWS
class TNXEcProAsdu1GWS : public TNXEcProAsdu1 {
protected:
    // 实现国网特定的转换逻辑
    virtual int _CvtNxEventSubFiledToAsdu1Info() override {
        // 1. 获取变电站地址
        const SUBSTATION_TB* pStation = m_pModelSeek->GetSubStationBasicCfg();
        
        // 2. 特定的转换逻辑
        while (ite != m_pEventMsg->list_subfields.end()) {
            // GW104特有的处理逻辑
        }
        
        // 3. 返回结果
        return result;
    }
};
```

### 模板方法的优势

1. 代码复用：
   - 基类定义通用算法结构
   - 子类只需实现特定步骤
   - 避免重复编写相同的框架代码

2. 扩展性：
   - 新增规约只需继承基类
   - 实现必要的虚函数
   - 保持整体结构不变

3. 维护性：
   - 算法骨架集中在基类
   - 修改通用逻辑只需改基类
   - 子类修改不影响其他规约

### 关键实现点

1. 基类中的模板方法：
```cpp
int CNXEc60870CvtObj::ConvertProToEventMsg() {
    // 1. 通用的预处理
    PreProcess();
    
    // 2. 调用子类特定实现
    _CvtNxEventSubFiledToAsdu1Info();
    
    // 3. 通用的后处理
    PostProcess();
}
```

2. 子类中的具体步骤：
```cpp
int TNXEcProAsdu1GWS::_CvtNxEventSubFiledToAsdu1Info() {
    // 1. GW104特定的地址处理
    Asdu1.Addr.nSubstationAdd = nSubstationAdd;
    
    // 2. GW104特定的数据转换
    if (nEventType == NX_IED_EVENT_HARDTRAP_REPORT) {
        bGetInfoObj = __DoGetHardInfoObj();
    }
    
    // 3. GW104特定的结果处理
    return ProcessResult();
}
```

### 使用示例

1. 创建新规约：
```cpp
class NewProtocolConverter : public CNXEc60870CvtObj {
protected:
    virtual int _CvtNxEventSubFiledToAsdu1Info() override {
        // 实现新规约的转换逻辑
    }
    
    virtual bool _DoGetHardInfoObj() override {
        // 实现新规约的硬件信息获取
    }
};
```

2. 调用转换：
```cpp
void ConvertData() {
    CNXEc60870CvtObj* converter = new NewProtocolConverter();
    converter->ConvertProToEventMsg(data, result);
}
```

## 规约转换中的工厂方法模式

### 基本结构

```cpp
// 抽象产品：规约转换接口
class INXEcProConvertObj {
    virtual int ConvertProToEventMsg() = 0;
    virtual int ConvertProToCommonMsg() = 0;
};

// 具体产品：GW104规约转换实现
class CNXEc60870CvtObjGWS : public CNXEc60870CvtObj {
    virtual int ConvertProToEventMsg() override;
    virtual int ConvertProToCommonMsg() override;
};

// 抽象工厂：规约转换工厂基类
class CNXEc60870CvtFactory {
public:
    virtual INXEcProConvertObj* CreateProConvertObj() = 0;
    virtual bool DestroyProConvertObj(INXEcProConvertObj* obj) = 0;
};

// 具体工厂：GW104规约转换工厂
class CNXEc60870CvtFactoryGWS : public CNXEc60870CvtFactory {
public:
    virtual INXEcProConvertObj* CreateProConvertObj() override {
        return new CNXEc60870CvtObjGWS();
    }
    
    virtual bool DestroyProConvertObj(INXEcProConvertObj* obj) override {
        delete obj;
        return true;
    }
};
```

### 工厂方法模式的特点

1. 单一职责：
   - 每个工厂类只负责创建一种类型的产品
   - GW104工厂只创建GW104规约转换器
   - 其他规约需要创建对应的工厂类

2. 封装变化：
   - 产品的创建逻辑封装在工厂类中
   - 客户端代码只需要知道抽象接口
   - 添加新规约只需添加新的工厂类

3. 继承结构：
   ```
   CNXEc60870CvtFactory (抽象工厂)
           ↓
   CNXEc60870CvtFactoryGWS (具体工厂)
           ↓
   创建 CNXEc60870CvtObjGWS (具体产品)
   ```

### 为什么是工厂方法而不是抽象工厂

1. 工厂方法模式：
   - 一个工厂创建一种类型的产品
   - 通过继承来创建不同的产品
   - 专注于单一产品等级结构

2. 抽象工厂模式：
   - 一个工厂创建多个产品族
   - 每个工厂实现多个创建方法
   - 关注产品族的创建

3. 当前实现：
```cpp
// 工厂方法模式 - 只创建一种产品
class CNXEc60870CvtFactoryGWS {
    INXEcProConvertObj* CreateProConvertObj() {
        return new CNXEc60870CvtObjGWS();
    }
};

// 如果是抽象工厂模式，会是这样：
class IProtocolFactory {
    virtual IConverter* CreateConverter() = 0;
    virtual IParser* CreateParser() = 0;
    virtual IFormatter* CreateFormatter() = 0;
};
```

### 使用示例

1. 创建工厂：
```cpp
CNXEc60870CvtFactory* factory = new CNXEc60870CvtFactoryGWS();
```

2. 使用工厂创建产品：
```cpp
INXEcProConvertObj* converter = factory->CreateProConvertObj();
```

3. 使用产品：
```cpp
converter->ConvertProToEventMsg(data, result);
```

4. 销毁产品：
```cpp
factory->DestroyProConvertObj(converter);
```

### 扩展新规约

1. 创建新的转换器：
```cpp
class NewProtocolConverter : public INXEcProConvertObj {
    virtual int ConvertProToEventMsg() override;
};
```

2. 创建对应的工厂：
```cpp
class NewProtocolFactory : public CNXEc60870CvtFactory {
    virtual INXEcProConvertObj* CreateProConvertObj() override {
        return new NewProtocolConverter();
    }
};
```






