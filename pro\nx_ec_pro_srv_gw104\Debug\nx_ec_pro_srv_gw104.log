﻿生成启动时间为 2025/7/23 9:51:35。
     1>项目“D:\code\pro\nx_ec_pro_srv_gw104\nx_ec_pro_srv_gw104.vcxproj”在节点 2 上(build 个目标)。
     1>C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\Microsoft.CppBuild.targets(299,5): warning MSB8004: Output 目录未以斜杠结尾。此生成实例将添加斜杠，因为必须有这个斜杠才能正确计算 Output 目录。
     1>InitializeBuildStatus:
         正在创建“Debug\nx_ec_pro_srv_gw104.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         D:\vs2010\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _USE_32BIT_TIME_T /D _DEBUG /D __PLATFORM_MS_WIN__ /D _CRT_SECURE_NO_WARNINGS /D NX_EC_PROTOCOL_EXPORT /D __PLATFORM_OPEN_LINUX__ /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Fo"Debug\\" /Fd"Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\..\..\..\..\nx_common\CsgLogRecord.cpp ..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp ..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.cpp ..\..\..\..\..\thirdparty\tinyxml\tinystr.cpp ..\..\..\..\..\thirdparty\tinyxml\tinyxml.cpp ..\..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp ..\..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp ..\..\ec_common\EcTemplateFunc.cpp ..\..\ec_common\NXECObject.cpp ..\..\ec_common\NXLoadEcModelLib.cpp ..\ec_pro_common\EcProCommonFun.cpp ..\ec_pro_common\NXEcLoad60870FlowLib.cpp ..\ec_pro_common\NXEcLoadMsgOperationLib.cpp ..\ec_pro_common\NXEcLoadProOperationLib.cpp ..\ec_pro_common\NXEcProtocol.cpp ..\ec_pro_common\NXEcSrvProtocol.cpp ..\ec_pro_common_iec60870\NXEc60870CvtFactory.cpp ..\ec_pro_common_iec60870\NXEc60870CvtObj.cpp ..\ec_pro_common_iec60870\NXEcIec104ExplainFactory.cpp ..\ec_pro_common_iec60870\NXEcIec104ProExplain.cpp ..\ec_pro_common_iec60870\NXEcProAsdu.cpp ..\ec_pro_common_iec60870\NXEcProAsdu1.cpp ..\ec_pro_common_iec60870\NXEcProAsdu10.cpp ..\ec_pro_common_iec60870\NXEcProAsdu101.cpp ..\ec_pro_common_iec60870\NXEcProAsdu102.cpp ..\ec_pro_common_iec60870\NXEcProAsdu103.cpp ..\ec_pro_common_iec60870\NXEcProAsdu12.cpp ..\ec_pro_common_iec60870\NXEcProAsdu13.cpp ..\ec_pro_common_iec60870\NXEcProAsdu15.cpp ..\ec_pro_common_iec60870\NXEcProAsdu16.cpp ..\ec_pro_common_iec60870\NXEcProAsdu17.cpp ..\ec_pro_common_iec60870\NXEcProAsdu18.cpp ..\ec_pro_common_iec60870\NXEcProAsdu2.cpp ..\ec_pro_common_iec60870\NXEcProAsdu21.cpp ..\ec_pro_common_iec60870\NXEcProAsdu21_Direct.cpp ..\ec_pro_common_iec60870\NXEcProAsdu4.cpp ..\ec_pro_common_iec60870\NXEcProAsdu42.cpp ..\ec_pro_common_iec60870\NXEcProAsdu6.cpp ..\ec_pro_common_iec60870\NXEcProAsdu7.cpp ec_pro_srv_gw104_export.cpp NXEc60870CvtFactory_GWS.cpp NXEc60870CvtObj_GWS.cpp NXEcGW104SrvProtocol.cpp NXEcProAsdu101_GWS.cpp NXEcProAsdu103_GWS.cpp NXEcProAsdu10_GWS.cpp NXEcProAsdu12_GWS.cpp NXEcProAsdu13_GWS.cpp NXEcProAsdu15_GWS.cpp NXEcProAsdu16_GWS.cpp NXEcProAsdu17_GWS.cpp NXEcProAsdu1_GWS.cpp NXEcProAsdu21_Direct_GWS.cpp NXEcProAsdu42_GWS.cpp NXEcProAsdu7_GWS.cpp NXEcProXmlHdl.cpp nx_ec_pro_srv_gw104_modify_note.cpp
         nx_ec_pro_srv_gw104_modify_note.cpp
         NXEcProXmlHdl.cpp
     1>d:\code\pro\nx_ec_pro_srv_gw104\nxecproxmlhdl.h(3): fatal error C1083: 无法打开包括文件:“../../../../../platform_include/external/tinyxml/tinyxml.h”: No such file or directory
         NXEcProAsdu7_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu42_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu21_Direct_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu1_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu17_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu16_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu15_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu13_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu12_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu10_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu103_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu101_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcGW104SrvProtocol.cpp
     1>d:\code\ec_common\inxecprotocol.h(11): fatal error C1083: 无法打开包括文件:“CommuTransObj.h”: No such file or directory
         NXEc60870CvtObj_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEc60870CvtFactory_GWS.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         ec_pro_srv_gw104_export.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu7.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu6.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         正在生成代码...
         正在编译...
         NXEcProAsdu42.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu4.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu21_Direct.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu21.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu2.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu18.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu17.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu16.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu15.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu13.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu12.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu103.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu102.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu101.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu10.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu1.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcProAsdu.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcIec104ProExplain.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcIec104ExplainFactory.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEc60870CvtObj.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         正在生成代码...
         正在编译...
         NXEc60870CvtFactory.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcSrvProtocol.cpp
     1>d:\code\ec_common\inxecprotocol.h(11): fatal error C1083: 无法打开包括文件:“CommuTransObj.h”: No such file or directory
         NXEcProtocol.cpp
     1>d:\code\ec_common\inxecprotocol.h(11): fatal error C1083: 无法打开包括文件:“CommuTransObj.h”: No such file or directory
         NXEcLoadProOperationLib.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcLoadMsgOperationLib.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXEcLoad60870FlowLib.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         EcProCommonFun.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXLoadEcModelLib.cpp
     1>d:\code\ec_common\ec_common_def.h(12): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         NXECObject.cpp
     1>d:\code\ec_common\nxecobject.h(11): fatal error C1083: 无法打开包括文件:“os_platform_def.h”: No such file or directory
         EcTemplateFunc.cpp
     1>d:\code\ec_common\ectemplatefunc.h(11): fatal error C1083: 无法打开包括文件:“inxmb_def.h”: No such file or directory
         tinyxmlparser.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\thirdparty\tinyxml\tinyxmlparser.cpp”: No such file or directory
         tinyxmlerror.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\thirdparty\tinyxml\tinyxmlerror.cpp”: No such file or directory
         tinyxml.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\thirdparty\tinyxml\tinyxml.cpp”: No such file or directory
         tinystr.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\thirdparty\tinyxml\tinystr.cpp”: No such file or directory
         UnLibmngr_Gbk2Utf8.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\nx_common\UnLibmngr_Gbk2Utf8.cpp”: No such file or directory
         CsgLogRecordMngr.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\nx_common\CsgLogRecordMngr.cpp”: No such file or directory
         CsgLogRecord.cpp
     1>c1xx : fatal error C1083: 无法打开源文件:“..\..\..\..\..\nx_common\CsgLogRecord.cpp”: No such file or directory
         正在生成代码...
     1>已完成生成项目“D:\code\pro\nx_ec_pro_srv_gw104\nx_ec_pro_srv_gw104.vcxproj”(build 个目标)的操作 - 失败。

生成失败。

已用时间 00:00:02.73
