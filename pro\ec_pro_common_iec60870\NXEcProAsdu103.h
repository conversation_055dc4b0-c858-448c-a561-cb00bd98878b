/**********************************************************************
* NXEcProAsdu103.h         author:jjl      date:23/10/2013            
*---------------------------------------------------------------------
*  note: ASDU103报文转换处理头文件                                                                 
*  
**********************************************************************/

#ifndef _H_NXECPROASDU103_H_ 
#define _H_NXECPROASDU103_H_

#include "NXEcProAsdu.h"

/**
* @defgroup   TNXEcProAsdu103:ASDU103报文转换处理结点类
* @{
*/


/** @brief     二维列表PRO_FRAME_DATA*/
 typedef vector<PRO_FRAME_DATA> FRAME_VDATA_LIST;
/**
 * @brief      ASDU103报文转换处理基类
 * <AUTHOR>
 * @date       23/10/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class TNXEcProAsdu103:public TNXEcProAsdu
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	virtual ~TNXEcProAsdu103();

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu103(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:

	/**
	* @brief         直接从本地生成结果回应，如初始化配置;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult) ;

    /**
	* @brief         转换规约信息到NX通用消息结构
	* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
	* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
	* @param[out]    PRO_FRAME_BODY_LIST & lBody：保存生成的规约失败回应(服务端规约有效）
	* @return        >=0:成功 <0:失败
	*/
	virtual int ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lBody);

	/**
	* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
	* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
	* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult);

	////////////////////////////////////////////////////////////////////////保护方法
protected:
	/**
	* @brief		通用文件上送处理函数			
	* @param[in]     const char * cFileName:指定的文件名称
	* @param[in]    int nBeginSendPos:起始传输位置
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _GeneralFileHandle(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult);

    /**
	* @brief		CSV文件上送处理函数			
	* @param[in]     const char * cFileName:指定的文件名称
	* @param[in]    int nBeginSendPos:起始传输位置
    * @param[in]    int nAddr:设备地址
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _CSVFileHandle(IN const char * cFileName,IN int nBeginSendPos,IN int nAddr,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief		做成空文件报文,来响应文件不存在的情况.			
	* @param[in]    int nBeginSendPos:命令中的起始传输位置.
	* @param[in]    const char * cFileName:文件名称(不包含路径).
	* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _CvtEmptyFileToFrameBody(IN int nBeginSendPos,IN const char * cFileName,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief		将文件处理成规约消息体的可变部分.			
	* @param[in]    const char * cGeneralFilePathName:文件名(包含路径)
	* @param[in]    int nBeginSendPos:起始传输位置.
	* @param[in]    FILE_PROPERTY_INF * pFileInfo:文件信息.
	* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _CvtFileToFrameBody(IN const char * cGeneralFilePathName,IN int nBeginSendPos,IN FILE_PROPERTY_INF * pFileInfo,OUT PRO_FRAME_BODY_LIST & lResult);

	/**
	* @brief		根据传入的规约信息体的可变部分,做成规约信息体.并压入列表.
	* @param[in]    int nBeginSendPos:起始传输位置.
	* @param[in]    CHAR_HANDLE * phChar:单帧数据内容
	* @param[in]     in nLast: 后续帧标志位
	* @param[out]   PRO_FRAME_BODY_LIST & lResult:规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _FormatGeneralToFrameBody(IN int nBeginSendPos,IN CHAR_HANDLE * phChar,IN int nLast,OUT PRO_FRAME_BODY_LIST & lResult);
	////////////////////////////////////////////////////////////////////////私有方法
private:


	////////////////////////////////////////////////////////////////////////保护成员
protected:

	/** @brief         通用消息指针*/
	NX_COMMON_MESSAGE * m_pCommonMsg;

    PRO_FRAME_BODY * m_pBody;

	/** @brief         返回信息标示*/
	u_int8 m_nRii;

	/** @brief    命令报文中的FUN,INF保存，在做成回答报文时使用*/
	u_int8 m_nFun;
	u_int8 m_nInf;
	////////////////////////////////////////////////////////////////////////私有成员
private:
};


/** @} */ //OVER


#endif  // _H_NXECPROASDU103_H_