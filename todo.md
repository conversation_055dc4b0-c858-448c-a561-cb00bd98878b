TNXEcProAsdu101GWS
    该类需要新增处理SettingUp关键字，响应主站召唤定值文件列表；
    当识别到SettingUp关键字后，从数据库指定的根路径下找到Setting文件夹，并找到对应的设备文件夹，根据时间过滤，获取文件列表信息。

    修改函数涉及：DirectResFromLocal

    virtual int ___QueryGeneralFilesList_SettingUp(IN ASDU_TIME AsduTime,IN const char * cFindFileName,OUT FILE_PROPERTY_INF_LIST & GeneralFileList);
    获取定值文件文件夹下的对应时间范围文件,组成列表返回
    ASDU_TIME AsduTime:时间范围
    const char * cFindFileName:文件名
    FILE_PROPERTY_INF_LIST & GeneralFileList:文件信息列表
    0：成功，-1：失败

TNXEcProAsdu103GWS
    该类需要新增处理SettingUp关键字，响应主站召唤定值文件。

    修改函数涉及：DirectResFromLocal；

    virtual int _GeneralFileHandle_SettingUp(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN string & strFullFname);
    定值文件上送处理函数
    const char * cFileName:指定的文件名称
    int nBeginSendPos:起始传输位置
    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
    0：成功，-1：失败
    
TNXEcProAsdu106GWS
    virtual int ConvertEventMsgToPro(IN NX_EVENT_MESSAGE* pMsg,OUT PRO_FRAME_BODY_LIST & lBody) ;
    根据NX事件信息生成规约事件列表
    NX_EVENT_MESSAGE * pMsg :事件信结构指针
    PRO_FRAME_BODY_LIST  & lBody :规约信息体
    int 0-成功 其它失败
    0：成功，-1：失败

    virtual int FormatAsdu106Body(IN ASDU106_INFO &Asdu106Info,OUT PRO_FRAME_BODY_LIST  & lBody,IN int nReserve = 0);
    根据ASDU106信息结构格式化ASDU106报文体
    ASDU106_INFO &Asdu106Info:  asdu106信息结构体
    PRO_FRAME_BODY_LIST  & lBody :ASDU规约信息体列表
    int nReserve:备用
    0：成功，-1：失败