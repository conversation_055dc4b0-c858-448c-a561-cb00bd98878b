/**********************************************************************
* NXEcProAsdu102.cpp         author:jjl      date:24/10/2013            
*---------------------------------------------------------------------
*  note: ASDU102转换处理节点类实现文件定义                                                               
*  
**********************************************************************/

#include "NXEcProAsdu102.h"

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
TNXEcProAsdu102::~TNXEcProAsdu102()
{

}

/**
* @brief         构造函数 
* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
* @param[out]    CLogRecord * pLogRecord:日志对象指针
* @return        无
*/
TNXEcProAsdu102::TNXEcProAsdu102(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord)
	:TNXEcProAsdu(pSeekIns,pLogRecord)
{
	// 设置类名称
	_SetLogClassName("TNXEcProAsdu102");
}

/**
* @brief         转换规约信息到NX通用消息结构
* @param[in]     PRO_FRAME_BODY_LIST* pBodyList :规约信息体列表指针
* @param[out]    NX_COMMON_MSG_LIST & lMsg: 保存生成的通用消息列表
* @param[out]    PRO_FRAME_BODY_LIST & lResult：保存生成的规约失败回应(服务端规约有效）
* @return        >=0:成功 <0:失败
*/
int TNXEcProAsdu102::ConvertProToCommonMsg(IN PRO_FRAME_BODY_LIST* pBodyList,OUT NX_COMMON_MSG_LIST & lMsg,OUT PRO_FRAME_BODY_LIST & lResult)
{
	RcdErrLogWithParentClass("ConvertProToCommonMsg()方法在规约客户端有效,服务端暂不支持","TNXEcProAsdu102");

	return EC_PRO_CVT_NOSUPPORT;
}

/**
* @brief         根据NX通用信息及规约命令生成规约结果列表或根据通用消息生成规约命令
* @param[in]     NX_COMMON_MESSAGE * pMsg :通用信息结构指针
* @param[in][out]PRO_FRAME_BODY_LIST & lCmd:规约命令
* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
* @return        int 0-成功 其它失败
*/
int TNXEcProAsdu102::ConvertCommonMsgToPro(IN NX_COMMON_MESSAGE * pMsg,IN OUT PRO_FRAME_BODY_LIST & lCmd,OUT PRO_FRAME_BODY_LIST & lResult)
{
	return 0;
}