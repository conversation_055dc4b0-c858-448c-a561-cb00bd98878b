/**********************************************************************
* NXEcProAsdu103_GWS.h         author:yys      date:02/12/2020         
*---------------------------------------------------------------------
*  note: 国网ASDU103报文转换处理-简要故障报告                                                              
*  
**********************************************************************/

#ifndef _H_NXECPROASDU103_GWS_H_ 
#define _H_NXECPROASDU103_GWS_H_

#include "NXEcProAsdu103.h"
#include "IniOperate.h"
#include "UnLibmngr_Gbk2Utf8.h"

const int  MAX_CHAR_BUFF_LEN_ASDU103 = 1000 ; //用于缓存区最大长度.

/**
* @defgroup   TNXEcProAsdu103GWS:ASDU103报文转换处理结点继承类
* @{
*/
 
/**
 * @brief      国网IEC103/104中ASDU103的转换处理基类
 * <AUTHOR>
 * @date       02/12/2020
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */
class TNXEcProAsdu103GWS:public TNXEcProAsdu103
{
	///////////////////////////////////////////////////////////////构造、析构
public:
	
	/**
	* @brief         析构函数
	* @param[in]     无 
	* @param[out]    无
	* @return        无
	*/
	~TNXEcProAsdu103GWS(){};

    /**
	* @brief         构造函数 
	* @param[in]     INXEcSSModelSeek * pSeekIns:模型查询实例
	* @param[out]    CLogRecord * pLogRecord:日志对象指针
	* @return        无
	*/
	TNXEcProAsdu103GWS(IN INXEcSSModelSeek * pSeekIns,IN CLogRecord * pLogRecord);

	///////////////////////////////////////////////////////////////公用方法
public:	

	/**
	* @brief         直接从本地生成结果回应，如初始化配置;
	* @param[in]     PRO_FRAME_BODY * pBody :规约信息体指针
	* @param[out]    PRO_FRAME_BODY_LIST & lResult：本地生成的结果帧体列表
	* @return        int 0-成功 其它失败
	*/
	virtual int DirectResFromLocal(IN PRO_FRAME_BODY * pBody,OUT PRO_FRAME_BODY_LIST & lResult) ;
	/////////////////////////////////////////////////////////////////////保护方法
protected:
	/**
	* @brief		通用文件上送处理函数			
	* @param[in]     const char * cFileName:指定的文件名称
	* @param[in]    int nBeginSendPos:起始传输位置
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _GeneralFileHandle(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN bool &bIsRobot,IN string & strFullFname);

	/**
	* @brief		日志文件上送处理函数			
	* @param[in]     const char * cFileName:指定的文件名称
	* @param[in]    int nBeginSendPos:起始传输位置
	* @param[out]    PRO_FRAME_BODY_LIST & lResult :规约信息体列表
	* @return		0-执行成功；其他-执行失败
	**/
	virtual int _GeneralFileHandle_nxlog(IN const char * cFileName,IN int nBeginSendPos,OUT PRO_FRAME_BODY_LIST & lResult,IN string & strFullFname);

	/**
	* @brief         字符串不区分大小写匹配
	* @param[in]     const char * s : 匹配字符串
	* @param[in]     const char * s2 :匹配字符串
	* @return        
	*/
	char* _strstr_nocase(const char * s, const char * s2);

private:
	int	 __InitCfgFile();

	char * CvtUtf8ToGbk(const char * cObj);

	char * CvtGbkToUtf8(const char * cObj);
private:
	CZclLibmngr_Gbk2Utf8 m_ZclLibmngr_Gbk2Utf8;
	/** @brief    用于存放xml文件生成过程需要字符串编码转换的结果字符串*/
	char		 m_cChr[MAX_CHAR_BUFF_LEN_ASDU103];

	int m_nRobotFileCode;
};


/** @} */ //  TNXEcProAsdu103GWS OVER


#endif // _H_NXECPROASDU103_GW_H_